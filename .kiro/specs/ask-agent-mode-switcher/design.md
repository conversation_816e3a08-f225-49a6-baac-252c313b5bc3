# 设计文档

## 概述

本设计文档描述了在智能体模式下实现 ask/agent 模式切换功能的技术方案。该功能允许用户在同一对话会话中无缝切换两种交互模式，同时保持上下文连续性。

## 架构

### 整体架构图

```mermaid
graph TB
    A[用户界面层] --> B[模式切换组件]
    A --> C[输入文本区域]
    A --> D[代码块组件]
    
    B --> E[模式状态管理]
    C --> F[ComposerService]
    D --> G[文件操作服务]
    
    E --> H[会话状态存储]
    F --> I[文件写入控制]
    G --> J[Diff显示控制]
    
    H --> K[持久化存储]
    I --> L[updateEditFileMessageFileStatus]
    J --> M[差异可视化]
```

### 核心流程

1. **模式切换流程**：用户通过下拉选择器切换模式 → 更新会话状态 → 调整文件操作行为
2. **Ask模式流程**：接收代码建议 → 显示应用按钮 → 用户手动应用 → 更新文件状态
3. **Agent模式流程**：接收代码建议 → 自动应用文件更改 → 显示差异

## 组件和接口

### 1. 模式切换下拉组件 (ModeSwitcher)

**位置**: `webview-ui/src/logics/composer/components/ModeSwitcher/`

```typescript
interface ModeSwitcherProps {
  currentMode: 'ask' | 'agent';
  onModeChange: (mode: 'ask' | 'agent') => void;
  disabled?: boolean;
}

export const ModeSwitcher: React.FC<ModeSwitcherProps> = ({
  currentMode,
  onModeChange,
  disabled = false
}) => {
  // 下拉选择器实现
  // 显示当前模式
  // 处理模式切换事件
};
```

### 2. 增强的用户输入文本区域

**修改**: `webview-ui/src/logics/UserInputTextarea/UserInputTextArea.tsx`

```typescript
interface UserInputTextareaProps {
  // 现有属性...
  mode: "chat" | "composer";
  composerMode?: 'ask' | 'agent'; // 新增：智能体模式下的子模式
  onComposerModeChange?: (mode: 'ask' | 'agent') => void; // 新增：模式切换回调
}
```

### 3. 代码块应用按钮组件

**位置**: `webview-ui/src/logics/composer/components/CodeBlockApplyButton/`

```typescript
interface CodeBlockApplyButtonProps {
  messageId: string;
  filepath: string;
  content: string;
  isApplied: boolean;
  onApply: (messageId: string, filepath: string) => void;
}

export const CodeBlockApplyButton: React.FC<CodeBlockApplyButtonProps> = ({
  messageId,
  filepath,
  content,
  isApplied,
  onApply
}) => {
  // 应用按钮实现
  // 处理应用事件
  // 状态管理
};
```

### 4. 增强的 ComposerService

**修改**: `src/services/composer/index.ts`

```typescript
class ComposerService {
  // 新增：当前会话的模式状态
  private currentComposerMode: 'ask' | 'agent' = 'agent';
  
  // 新增：模式切换方法
  public switchComposerMode(mode: 'ask' | 'agent'): void {
    this.currentComposerMode = mode;
    // 更新会话状态
    // 通知前端更新
  }
  
  // 修改：文件写入控制
  private shouldApplyFileChanges(): boolean {
    return this.currentComposerMode === 'agent';
  }
  
  // 修改：增强的文件状态更新
  private updateEditFileMessageFileStatus(
    session: PersistedComposerSessionData,
    selector: (message: InternalLocalMessage_Tool_EditFile) => boolean,
    status: "accepted" | "rejected" | "init",
    mode?: 'ask' | 'agent' // 新增：模式标识
  ) {
    // 现有实现 + 模式标识
  }
}
```

## 数据模型

### 1. 会话状态扩展

```typescript
interface ComposerSessionState {
  // 现有字段...
  composerMode: 'ask' | 'agent'; // 新增：当前模式
  modeHistory: Array<{
    mode: 'ask' | 'agent';
    timestamp: number;
    messageId?: string;
  }>; // 新增：模式切换历史
}
```

### 2. 消息状态扩展

```typescript
interface InternalLocalMessage_Tool_EditFile {
  // 现有字段...
  appliedMode?: 'ask' | 'agent'; // 新增：应用时的模式
  isManuallyApplied?: boolean; // 新增：是否手动应用
}
```

### 3. 文件状态扩展

```typescript
interface FilePersistedStateType {
  // 现有字段...
  appliedMode?: 'ask' | 'agent'; // 新增：应用模式标识
  applySource: 'auto' | 'manual'; // 新增：应用来源
}
```

## 错误处理

### 1. 模式切换错误

- **场景**: 在文件操作进行中切换模式
- **处理**: 等待当前操作完成后再切换，显示等待提示

### 2. 文件应用错误

- **场景**: Ask模式下手动应用代码失败
- **处理**: 显示错误提示，保持应用按钮可用状态

### 3. 状态同步错误

- **场景**: 前后端模式状态不一致
- **处理**: 定期同步状态，出现不一致时以后端状态为准

## 测试策略

### 1. 单元测试

- **ModeSwitcher组件**: 测试模式切换逻辑
- **CodeBlockApplyButton组件**: 测试应用按钮状态管理
- **ComposerService**: 测试文件操作控制逻辑

### 2. 集成测试

- **模式切换流程**: 测试完整的模式切换和状态保持
- **文件应用流程**: 测试Ask模式下的手动应用流程
- **状态同步**: 测试前后端状态同步机制

### 3. 端到端测试

- **用户交互流程**: 模拟用户完整的使用场景
- **跨会话状态**: 测试会话切换时的状态保持
- **错误恢复**: 测试各种错误场景的恢复机制

## 实现细节

### 1. 模式切换实现

```typescript
// 前端模式切换处理
const handleModeChange = useCallback((newMode: 'ask' | 'agent') => {
  // 1. 更新本地状态
  setComposerMode(newMode);
  
  // 2. 通知后端
  kwaiPilotBridgeAPI.composer.switchMode(newMode);
  
  // 3. 更新UI状态
  updateUIForMode(newMode);
}, []);

// 后端模式切换处理
public async switchComposerMode(mode: 'ask' | 'agent') {
  // 1. 验证切换条件
  if (this.isOperationInProgress()) {
    throw new Error('操作进行中，无法切换模式');
  }
  
  // 2. 更新内部状态
  this.currentComposerMode = mode;
  
  // 3. 更新会话数据
  await this.updateSessionMode(mode);
  
  // 4. 通知前端
  this.notifyModeChanged(mode);
}
```

### 2. 文件操作控制

```typescript
// 在文件写入操作中添加模式检查
private async handleFileEdit(message: InternalLocalMessage_Tool_EditFile) {
  if (this.currentComposerMode === 'ask') {
    // Ask模式：只更新消息状态，不实际写入文件
    await this.updateEditFileMessageFileStatus(
      session, 
      m => m.ts === message.ts, 
      "init",
      'ask'
    );
    return; // 提前返回，不执行文件写入
  }
  
  // Agent模式：正常执行文件写入
  await this.performFileWrite(message);
  await this.updateEditFileMessageFileStatus(
    session, 
    m => m.ts === message.ts, 
    "accepted",
    'agent'
  );
}
```

### 3. 应用按钮状态管理

```typescript
// 代码块组件中的应用按钮逻辑
const CodeBlockWithApplyButton: React.FC<Props> = ({ message, content }) => {
  const { composerMode } = useComposerState();
  const [isApplied, setIsApplied] = useState(false);
  
  // 只在Ask模式下显示应用按钮
  const showApplyButton = composerMode === 'ask' && !isApplied;
  
  const handleApply = useCallback(async () => {
    try {
      await kwaiPilotBridgeAPI.composer.applyCodeBlock({
        messageId: message.ts,
        filepath: message.filepath,
        content
      });
      setIsApplied(true);
    } catch (error) {
      // 错误处理
    }
  }, [message, content]);
  
  return (
    <div>
      <CodeBlock content={content} />
      {showApplyButton && (
        <CodeBlockApplyButton onApply={handleApply} />
      )}
    </div>
  );
};
```

### 4. 历史记录过滤

```typescript
// 工作区会话过滤逻辑
const filterWorkspaceConversations = (conversations: Conversation[]) => {
  const currentWorkspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  
  return conversations.filter(conv => 
    conv.workspacePath === currentWorkspacePath
  );
};

// 只读模式处理
const ConversationView: React.FC<Props> = ({ conversation, isHistorical }) => {
  const isReadOnly = isHistorical && isInAgentMode;
  
  return (
    <div>
      <ConversationMessages messages={conversation.messages} />
      {!isReadOnly && <UserInputTextarea />}
    </div>
  );
};
```