# 实现计划

- [ ] 1. 创建模式切换下拉组件
  - 在 `webview-ui/src/logics/composer/components/` 下创建 `ModeSwitcher` 组件
  - 实现下拉选择器UI，包含 "ask" 和 "agent" 选项
  - 添加模式切换事件处理逻辑
  - 实现组件的样式和交互状态
  - _需求: 1.1, 1.2_

- [ ] 2. 扩展会话状态数据结构
  - 修改 `ComposerSessionState` 接口，添加 `composerMode` 字段
  - 添加 `modeHistory` 字段记录模式切换历史
  - 更新会话状态的序列化和反序列化逻辑
  - 实现状态迁移逻辑，确保向后兼容
  - _需求: 1.2, 6.6_

- [ ] 3. 增强 ComposerService 的模式管理功能
  - 在 `ComposerService` 类中添加 `currentComposerMode` 属性
  - 实现 `switchComposerMode` 方法处理模式切换
  - 添加 `shouldApplyFileChanges` 方法控制文件写入行为
  - 修改现有文件操作方法，在 ask 模式下提前返回
  - _需求: 1.3, 1.4, 2.1_

- [ ] 4. 修改文件状态更新机制
  - 扩展 `updateEditFileMessageFileStatus` 方法，添加模式参数
  - 修改 `InternalLocalMessage_Tool_EditFile` 接口，添加模式标识字段
  - 更新 `FilePersistedStateType` 接口，添加应用模式和来源字段
  - 实现模式区分的状态更新逻辑
  - _需求: 6.2, 6.3, 6.4_

- [ ] 5. 创建代码块应用按钮组件
  - 在 `webview-ui/src/logics/composer/components/` 下创建 `CodeBlockApplyButton` 组件
  - 实现应用按钮的UI和交互逻辑
  - 添加应用状态管理（已应用/未应用）
  - 实现点击应用时的代码应用逻辑
  - _需求: 2.2, 2.3, 2.4_

- [ ] 6. 集成模式切换器到用户输入区域
  - 修改 `UserInputTextarea` 组件，添加 `composerMode` 和 `onComposerModeChange` 属性
  - 在输入框左下角区域集成 `ModeSwitcher` 组件
  - 实现模式状态的传递和更新
  - 添加模式切换时的UI反馈
  - _需求: 1.1, 1.5_

- [ ] 7. 修改代码块组件以支持应用按钮
  - 修改 `EditFile` 组件，根据模式显示或隐藏应用按钮
  - 集成 `CodeBlockApplyButton` 组件到代码块中
  - 实现应用按钮的条件显示逻辑（仅在 ask 模式且未应用时显示）
  - 添加应用成功后的状态更新
  - _需求: 2.1, 2.2, 2.4_

- [ ] 8. 实现手动代码应用功能
  - 在 `ComposerService` 中添加 `applyCodeBlock` 方法
  - 实现 ask 模式下的手动文件写入逻辑
  - 添加应用后的 diff 显示功能
  - 实现应用状态的持久化存储
  - _需求: 2.3, 2.5_

- [ ] 9. 添加前后端通信接口
  - 在 bridge 协议中添加模式切换相关的消息类型
  - 实现前端到后端的模式切换通信
  - 添加代码块应用的通信接口
  - 实现状态同步机制
  - _需求: 1.2, 2.3, 6.6_

- [ ] 10. 实现问答面板隐藏功能
  - 识别当前是否处于智能体模式
  - 在智能体模式下隐藏问答面板相关UI组件
  - 实现模式切换时问答面板的显示/隐藏切换
  - 确保隐藏问答面板不影响主对话功能
  - _需求: 3.1, 3.2, 3.3_

- [ ] 11. 实现工作区历史记录过滤
  - 修改历史记录查询逻辑，添加工作区路径过滤
  - 在智能体模式下只显示当前工作区的对话
  - 实现工作区切换时的历史记录更新
  - 添加空状态处理（无工作区对话时）
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 12. 实现历史对话只读模式
  - 识别历史对话和当前对话的区别
  - 在历史对话中隐藏输入框和交互元素
  - 添加只读模式的视觉指示
  - 实现从只读对话返回时的状态恢复
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 13. 实现文件更改列表的模式区分显示
  - 修改文件更改列表组件，显示更改来源（ask/agent）
  - 添加视觉区分不同模式的更改
  - 实现模式切换时更改列表的状态保持
  - 确保更改跟踪的准确性
  - _需求: 6.1, 6.5, 6.6_

- [ ] 14. 添加错误处理和边界情况处理
  - 实现模式切换时的操作冲突检测
  - 添加文件应用失败的错误处理
  - 实现状态同步失败的恢复机制
  - 添加用户友好的错误提示
  - _需求: 所有需求的错误处理_

- [ ] 15. 编写单元测试
  - 为 `ModeSwitcher` 组件编写测试
  - 为 `CodeBlockApplyButton` 组件编写测试
  - 为 `ComposerService` 的新功能编写测试
  - 为状态管理逻辑编写测试
  - _需求: 所有需求的测试覆盖_

- [ ] 16. 集成测试和端到端测试
  - 测试完整的模式切换流程
  - 测试 ask 模式下的代码应用流程
  - 测试历史记录过滤和只读模式
  - 测试错误场景和恢复机制
  - _需求: 所有需求的集成测试_