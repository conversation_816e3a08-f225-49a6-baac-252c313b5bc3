# 需求文档

## 介绍

此功能使用户能够在智能体模式下的单个对话会话中无缝切换 ask 和 agent 模式。用户可以在保持上下文连续性的同时切换不同的交互模式 - 使用 agent 模式进行自主代码修改，使用 ask 模式获取代码建议而不自动应用。

## 需求

### 需求 1

**用户故事：** 作为使用智能体模式的开发者，我希望在同一个对话会话中切换 ask 和 agent 模式，以便在选择不同交互方式时保持上下文连贯。

#### 验收标准

1. 当用户处于智能体模式时，系统应在左下角输入区域显示包含 "ask" 和 "agent" 选项的下拉选择器
2. 当用户在 ask 和 agent 模式之间切换时，系统应保持对话上下文和历史记录
3. 当用户选择 "agent" 模式时，系统应表现得与当前 agent 模式完全相同（自动文件写入和 diff 显示）
4. 当用户选择 "ask" 模式时，系统应在文件写入和 diff 显示操作处提前返回
5. 当用户在对话中途切换模式时，系统应保留所有之前的消息和上下文

### 需求 2

**用户故事：** 作为在智能体模式下使用 ask 模式的开发者，我希望在未应用的代码块上看到应用按钮，以便选择性地应用建议的代码更改。

#### 验收标准

1. 当用户处于 ask 模式并接收到代码建议时，系统应显示代码块而不自动应用它们
2. 当 ask 模式下的代码块尚未应用时，系统应在该代码块上显示"应用"按钮
3. 当用户点击"应用"按钮时，系统应应用代码更改并显示 diff
4. 当代码块已被应用时，系统应隐藏该特定代码块的"应用"按钮
5. 当用户在 ask 模式下应用代码时，系统应显示与 agent 模式相同的 diff 可视化

### 需求 3

**用户故事：** 作为开发者，我希望在智能体模式下隐藏问答面板，以便获得专注于对话的更清洁界面。

#### 验收标准

1. 当用户进入智能体模式时，系统应隐藏问答面板
2. 当用户退出智能体模式时，系统应恢复问答面板的可见性
3. 当问答面板被隐藏时，系统不应影响主对话界面的功能

### 需求 4

**用户故事：** 作为开发者，我希望在智能体模式下的历史列表中只看到当前工作区的对话，以便专注于相关对话。

#### 验收标准

1. 当用户处于智能体模式时，系统应在历史列表中只显示来自当前工作区的对话
2. 当显示工作区特定历史时，系统应按时间顺序显示对话
3. 当用户切换工作区时，系统应更新历史列表以只显示新工作区的对话
4. 当不存在工作区对话时，系统应显示适当的空状态

### 需求 5

**用户故事：** 作为在智能体模式下查看历史对话的开发者，我希望以只读模式查看它们而不具备输入功能，以便查看过去的对话而不会意外修改它们。

#### 验收标准

1. 当用户在智能体模式下打开历史对话时，系统应以只读模式显示
2. 当对话处于只读模式时，系统应隐藏输入框
3. 当对话处于只读模式时，系统应禁用除导航外的所有交互元素
4. 当用户从只读对话导航离开时，系统应为新对话恢复正常输入功能
5. 当显示只读对话时，系统应向用户清楚地指示只读状态

### 需求 6

**用户故事：** 作为在 ask 和 agent 模式之间切换的开发者，我希望系统正确管理文件更改状态和编辑消息状态，以便在不同模式下正确跟踪文件修改。

#### 验收标准

1. 当用户从 agent 模式切换到 ask 模式时，系统应保留之前 agent 操作的现有文件更改状态
2. 当用户在 ask 模式下应用代码时，系统应使用适合 ask 模式应用的状态调用 `updateEditFileMessageFileStatus`
3. 当用户处于 agent 模式时，系统应使用 agent 特定的状态更新调用 `updateEditFileMessageFileStatus`
4. 当在 ask 模式下进行文件更改时，系统应在更改列表中区分文件状态与 agent 模式更改
5. 当显示文件更改列表时，系统应清楚地指示哪些更改来自 ask 模式与 agent 模式
6. 当用户切换模式时，系统应保持准确的文件更改跟踪而不丢失之前的修改状态