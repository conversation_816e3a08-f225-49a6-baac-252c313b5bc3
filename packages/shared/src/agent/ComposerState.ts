import { SupportedModels } from "./supportedModels";
import { FileIndeterminateStateType, InternalLocalMessage } from "./types";
import { OutPutBlockCodePath } from "../misc/blockcode";

export interface IndeterminatedWorkingSetEffectState {
  messageTs: number;
  state: FileIndeterminateStateType;
  path: string;
}

// 会话缓存路径信息类型
export type SessionCachePathInfo = OutPutBlockCodePath & {
  order: number;
  cacheKey?: string;
};

export interface ComposerState {
  localMessages: InternalLocalMessage[];
  /** 对话所属的工作区 uri，用于区分不同工作区的对话, 如果用户没有打开工作区，则为无归属的对话，使用空字符串表示 */
  workspaceUri: string;
  /** 会话 id */
  sessionId: string;
  /** 当前任务是否被中断, 用于执行 resume */
  currentTaskInterrupted: boolean;
  /** 当前对话的 indeterminated 状态 */
  indeterminatedWorkingSetEffects: IndeterminatedWorkingSetEffectState[];
  /** 当前对话是否是工作区对话 */
  isCurrentWorkspaceSession: boolean;
  /** 正在编辑的消息的 ts(role=user), 用于从历史某个消息重新开始对话 */
  editingMessageTs: number | undefined;
  /** 当前消息的 ts(type=checkpoint_created), restore 时会修改currentMessageTs。为空则表示取最新的消息，否则是否某个历史checkpoint_created */
  currentMessageTs: number | undefined;
  /** 当前对话的模型 */
  userPreferredModel: SupportedModels;
  /** 和本地服务断开链接 */
  localServiceConnectionLost: boolean;
  /** 索引构建状态 */
  indexed?: boolean;
  /** 会话缓存路径信息 */
  cachePathInfos?: SessionCachePathInfo[];
}
